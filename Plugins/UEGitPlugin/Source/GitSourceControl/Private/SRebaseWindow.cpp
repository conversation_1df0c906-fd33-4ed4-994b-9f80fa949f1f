// Copyright (c) 2014-2018 <PERSON><PERSON><PERSON> (<EMAIL>)
//
// Distributed under the MIT License (MIT) (See accompanying file LICENSE.txt
// or copy at http://opensource.org/licenses/MIT)

#include "SRebaseWindow.h"
#include "Runtime/Launch/Resources/Version.h"

#include "Fonts/SlateFontInfo.h"
#include "Misc/App.h"
#include "Styling/SlateTypes.h"
#include "Widgets/SBoxPanel.h"
#include "Widgets/Text/STextBlock.h"
#include "Widgets/Input/SButton.h"
#include "Widgets/Input/SCheckBox.h"
#include "Widgets/Input/SEditableTextBox.h"
#include "Widgets/Input/SMultiLineEditableTextBox.h"
#include "Widgets/Layout/SBorder.h"
#include "Widgets/Layout/SSeparator.h"
#if ENGINE_MAJOR_VERSION == 5 && ENGINE_MINOR_VERSION >= 1
#include "Styling/AppStyle.h"
#else
#include "EditorStyleSet.h"
#endif
#include "Widgets/Images/SThrobber.h"
#include "Widgets/Layout/SUniformGridPanel.h"
#include "GitSourceControlUtils.h"
#include "AssetToolsModule.h"
#if ENGINE_MAJOR_VERSION >= 5
#include "AssetRegistry/AssetRegistryModule.h"
#else
#include "AssetRegistryModule.h"
#endif

#define LOCTEXT_NAMESPACE "SRebaseWindow"

namespace SRebaseWindowDefs
{
	const FName ColumnID_RebaseFilePath("RebaseFilePath");
	const FName ColumnID_ResolveConflict("ResolveConflict");
	const FName ColumnID_CheckBox("CheckBox");
	const FName ColumnID_UseMine("UseMine");
	const FName ColumnID_UseTheirs("UseTheirs");

	const float CheckBoxColumnWidth = 23.0f;
	const float IconColumnWidth = 21.0f;
}

FRebaseItem::FRebaseItem(const FString& RebaseFilePathV, const bool IsConflictVV)
{
	RebaseFilePath = RebaseFilePathV;
	IsConflictV = IsConflictVV;
}

FReply FRebaseItem::OnResolveConflictClick()
{
	FString V = ConvertPackagePathToAssetPath(RebaseFilePath);
	FAssetToolsModule& AssetToolsModule = FModuleManager::GetModuleChecked<FAssetToolsModule>("AssetTools");

	IAssetRegistry& AssetRegistry = FModuleManager::LoadModuleChecked<FAssetRegistryModule>(TEXT("AssetRegistry")).Get();
#if ENGINE_MAJOR_VERSION == 5 && ENGINE_MINOR_VERSION >= 1
	FAssetData AssetData = AssetRegistry.GetAssetByObjectPath(FSoftObjectPath(V));
#else
	FAssetData AssetData = AssetRegistry.GetAssetByObjectPath(*V);
#endif

	UObject* CurrentObject = AssetData.GetAsset();
	if (CurrentObject)
	{
		const FString PackagePath = AssetData.PackageName.ToString();
		const FString PackageName = AssetData.AssetName.ToString();
		auto AssetTypeActions = AssetToolsModule.Get().GetAssetTypeActionsForClass(CurrentObject->GetClass()).Pin();
		if (AssetTypeActions.IsValid())
		{
			AssetTypeActions->Merge(CurrentObject);
		}
	}

	return FReply::Handled();
}

FString FRebaseItem::ConvertPackagePathToAssetPath(const FString& Filename)
{
	FString EngineFileName = Filename;
	FString GameFileName = Filename;
	if (FPaths::MakePathRelativeTo(EngineFileName, *FPaths::EngineContentDir()) && !EngineFileName.Contains(TEXT("../")))
	{
		const FString ShortName = FPaths::GetBaseFilename(EngineFileName);
		const FString PathName = FPaths::GetPath(EngineFileName);
		const FString AssetName = FString::Printf(TEXT("/Engine/%s/%s.%s"), *PathName, *ShortName, *ShortName);
		return AssetName;
	}
	else if (FPaths::MakePathRelativeTo(GameFileName, *FPaths::ProjectContentDir()) && !GameFileName.Contains(TEXT("../")))
	{
		const FString ShortName = FPaths::GetBaseFilename(GameFileName);
		const FString PathName = FPaths::GetPath(GameFileName);
		const FString AssetName = FString::Printf(TEXT("/Game/%s/%s.%s"), *PathName, *ShortName, *ShortName);
		return AssetName;
	}
	else
	{
		//CODE_UE_LOG(LogAutomationEditorCommon, Error, TEXT("PackagePath (%s) is invalid for the current project"), *PackagePath);
		return TEXT("");
	}
}

FReply FRebaseItem::OnBranchClick(FString TypeVal)
{
	FString action;
	// Consistent with the behavior of ugit software
	action = "--ours";
	if (!RebaseFilePath.IsEmpty())
	{
		GitSourceControlUtils::CheckoutByStrategyInOperation({ RebaseFilePath }, action, TypeVal);
	}
	return FReply::Handled();
}
FReply FRebaseItem::OnStashClick(FString TypeVal)
{
	FString action;
	// Consistent with the behavior of ugit software
	action = "--theirs";
	if (!RebaseFilePath.IsEmpty())
	{
		GitSourceControlUtils::CheckoutByStrategyInOperation({ RebaseFilePath }, action, TypeVal);
	}
	return FReply::Handled();
}
FReply SRebaseWindow::OnSelectBranchClick(const FString Type)
{
	FString action;
	// Consistent with the behavior of ugit software
	action = "--ours";
	TArray<FString> RebaseFiles;
	for (auto& ItSelectedItems : ListViewItems)
	{
		if (ItSelectedItems->bCheckBoxStatus)
		{
			FString RebaseFile = ItSelectedItems->GetDisplayName().ToString();
			if (!RebaseFile.IsEmpty())
			{
				RebaseFiles.Add(RebaseFile);
			}
		}
	}
	if (RebaseFiles.Num() > 0)
	{
		GitSourceControlUtils::CheckoutByStrategyInOperation(RebaseFiles, action, TypeVal);
		return FReply::Handled();
	}
	GitSourceControlUtils::DisplaySuccessNotification(TEXT("Please select the items that need conflict resolution."), false);
	return FReply::Handled();
}

FReply SRebaseWindow::OnSelectStashClick(const FString Type)
{
	FString action;
	// Consistent with the behavior of ugit software
	action = "--theirs";
	TArray<FString> RebaseFiles;
	for (auto& ItSelectedItems : ListViewItems)
	{
		if (ItSelectedItems->bCheckBoxStatus)
		{
			FString RebaseFile = ItSelectedItems->GetDisplayName().ToString();
			if (!RebaseFile.IsEmpty())
			{
				RebaseFiles.Add(RebaseFile);
			}
		}
	}
	if (RebaseFiles.Num() > 0)
	{
		GitSourceControlUtils::CheckoutByStrategyInOperation(RebaseFiles, action, TypeVal);
		return FReply::Handled();
	}
	GitSourceControlUtils::DisplaySuccessNotification(TEXT("Please select the items that need conflict resolution."), false);
	return FReply::Handled();
}
void SRebaseWindow::OnCheckAllCheckBox(ECheckBoxState NewState)
{
	if (NewState == ECheckBoxState::Checked)
	{
		for (auto& Items : ListViewItems)
		{
			Items->bCheckBoxStatus = true;
		}
	}
	else
	{
		for (auto& Items : ListViewItems)
		{
			Items->bCheckBoxStatus = false;
		}
	}

}
void FRebaseItem::OnCheckCheckBox(ECheckBoxState NewState)
{
	if (NewState == ECheckBoxState::Checked)
	{
		bCheckBoxStatus = true;
	}
	else
	{
		bCheckBoxStatus = false;
	}

}


FReply SRebaseWindow::OnFlashWindowClick()
{
	GitSourceControlUtils::FlashWindow();
	return FReply::Handled();
}

FReply SRebaseWindow::OnContinueClick(const FString Type)
{
	if (Type.Equals("REBASE"))
	{
		GitSourceControlUtils::RebaseFile("--continue");
	}
	else if (Type.Equals("MERGE"))
	{
		GitSourceControlUtils::MergeCheckInFile("MERGE");
	}
	
	return FReply::Handled();
}

FReply SRebaseWindow::OnAbortClick(const FString Type)
{
	if (Type.Equals("REBASE"))
	{
		GitSourceControlUtils::RebaseFile("--abort");
	}
	else if (Type.Equals("MERGE"))
	{
		GitSourceControlUtils::MergeAbortFile();
	}
	
	return FReply::Handled();
}

FReply SRebaseWindow::OnCloseClick()
{
	GitSourceControlUtils::CloseWindow();
	return FReply::Handled();
}

void SRebaseWindow::Construct(const FArguments& InArgs, TMap<FString, TSharedRef<class FGitSourceControlState, ESPMode::ThreadSafe> > StateCache, FString TypeV)
{
	if (!TypeV.IsEmpty())
	{
		TypeVal = TypeV;
	}

#if ENGINE_MAJOR_VERSION == 5 && ENGINE_MINOR_VERSION >= 1
	const FSlateFontInfo Font = FAppStyle::GetFontStyle(TEXT("SourceControl.LoginWindow.Font"));
#else
	const FSlateFontInfo Font = FEditorStyle::GetFontStyle(TEXT("SourceControl.LoginWindow.Font"));
#endif

	const FText FileFilterType = NSLOCTEXT("GitSourceControl", "Executables", "Executables");
#if PLATFORM_WINDOWS
	const FString FileFilterText = FString::Printf(TEXT("%s (*.exe)|*.exe"), *FileFilterType.ToString());
#else
	const FString FileFilterText = FString::Printf(TEXT("%s"), *FileFilterType.ToString());
#endif

	TSharedRef<SHeaderRow> HeaderRowWidget = SNew(SHeaderRow);

	/*for (const auto& Item : InArgs._Items.Get())
	{
		ListViewItems.Add(MakeShareable(new FRebaseItem(Item)));
	}*/

	int32 Num = 0;
	
	for (auto It = StateCache.CreateIterator(); It; ++It)
	{
		if (It->Value.Get().GetWorkingCopyState() == EWorkingCopyState::Conflicted)
		{
			bool conflictV = It->Value.Get().GetWorkingCopyState() == EWorkingCopyState::Conflicted;
			ListViewItems.Add(MakeShareable(new FRebaseItem(It->Key, conflictV)));

			Num++;
		}
	}

	FNumberFormattingOptions NoCommas;
	NoCommas.UseGrouping = false;
	const FText Key = FText::Format(LOCTEXT("RebaseFilePathColumnLabel", "{0} Conflict File Path"), FText::AsNumber(Num, &NoCommas));
	HeaderRowWidget->AddColumn(
		SHeaderRow::Column(SRebaseWindowDefs::ColumnID_CheckBox)
		.DefaultLabel(LOCTEXT("AllSCheckBox","All"))
		.FillWidth(0.2f)
		[
			SNew(SCheckBox)
			.OnCheckStateChanged(this,&SRebaseWindow::OnCheckAllCheckBox)
		]
	);
	HeaderRowWidget->AddColumn(
		SHeaderRow::Column(SRebaseWindowDefs::ColumnID_RebaseFilePath)
		.HeaderContentPadding(FMargin(8.0f, 8.0f, 8.0f, 8.0f))
		.DefaultLabel(Key)
		//.SortMode(this, &SSourceControlSubmitWidget::GetColumnSortMode, SSourceControlSubmitWidgetDefs::ColumnID_FileLabel)
		//.OnSort(this, &SSourceControlSubmitWidget::OnColumnSortModeChanged)
		.FillWidth(4.0f)
	);

	//HeaderRowWidget->AddColumn(
	//	SHeaderRow::Column(SRebaseWindowDefs::ColumnID_ResolveConflict)
	//	.HeaderContentPadding(FMargin(8.0f, 8.0f, 8.0f, 8.0f))
	//	.DefaultLabel(LOCTEXT("ResolveConflictColumnLabel", ""))
	//	//.SortMode(this, &SSourceControlSubmitWidget::GetColumnSortMode, SSourceControlSubmitWidgetDefs::ColumnID_FileLabel)
	//	//.OnSort(this, &SSourceControlSubmitWidget::OnColumnSortModeChanged)
	//	.FillWidth(1.0f)
	//);
	
	HeaderRowWidget->AddColumn(
		SHeaderRow::Column(SRebaseWindowDefs::ColumnID_UseMine)
		.HeaderContentPadding(FMargin(8.0f, 8.0f, 8.0f, 8.0f))
		.DefaultLabel(LOCTEXT("UseMineColumnLabel", ""))
		//.SortMode(this, &SSourceControlSubmitWidget::GetColumnSortMode, SSourceControlSubmitWidgetDefs::ColumnID_FileLabel)
		//.OnSort(this, &SSourceControlSubmitWidget::OnColumnSortModeChanged)
		.FillWidth(1.0f)
		[
			SNew(SButton)
			.Text(LOCTEXT("UseMineCheck", "Use checked branch version"))
			.ToolTipText(LOCTEXT("UseMineCheckToolTip", "Selected resource file, Use branch versions to resolve conflicts "))
			.OnClicked(this, &SRebaseWindow::OnSelectBranchClick, TypeVal)
			.HAlign(HAlign_Center)
			.VAlign(VAlign_Center)
		]
	);
	
	HeaderRowWidget->AddColumn(
		SHeaderRow::Column(SRebaseWindowDefs::ColumnID_UseTheirs)
		.HeaderContentPadding(FMargin(8.0f, 8.0f, 8.0f, 8.0f))
		.DefaultLabel(LOCTEXT("UseTheirsColumnLabel", ""))
		//.SortMode(this, &SSourceControlSubmitWidget::GetColumnSortMode, SSourceControlSubmitWidgetDefs::ColumnID_FileLabel)
		//.OnSort(this, &SSourceControlSubmitWidget::OnColumnSortModeChanged)
		.FillWidth(1.0f)
		[
			SNew(SButton)
			.Text(LOCTEXT("UseTheirsCheck", "Use checked [Stash] version"))
			.ToolTipText(LOCTEXT("UseTheirsCheckToolTip", "Selected resource file, Use [Stash] versions to resolve conflicts "))
			.OnClicked(this, &SRebaseWindow::OnSelectStashClick, TypeVal)
			.HAlign(HAlign_Center)
			.VAlign(VAlign_Center)
		]
	);

	ChildSlot
		[
			SNew(SBorder)
#if ENGINE_MAJOR_VERSION == 5 && ENGINE_MINOR_VERSION >= 1
			.BorderImage(FAppStyle::GetBrush("DetailsView.CategoryBottom"))
#else
			.BorderImage(FEditorStyle::GetBrush("DetailsView.CategoryBottom"))
#endif
		.Padding(FMargin(0.0f, 0.0f, 0.0f, 0.0f))
		[
			SNew(SVerticalBox)
			//step 2
			+ SVerticalBox::Slot()
			.Padding(FMargin(0, 0))
			[
				SNew(SBorder)
				[
					SAssignNew(ListView, SListView<TSharedPtr<FRebaseItem>>)
					.ItemHeight(20)
					.ListItemsSource(&ListViewItems)
					.OnGenerateRow(this, &SRebaseWindow::OnGenerateRowForList)
					.HeaderRow(HeaderRowWidget)
					.SelectionMode(ESelectionMode::Single)
				]
			]

			//step3
			+ SVerticalBox::Slot()
				.AutoHeight()
				.Padding(16.0f, 16.0f, 16.0f, 16.0f)
				[
					SNew(SHorizontalBox)
					+ SHorizontalBox::Slot()
				.AutoWidth()
				.VAlign(VAlign_Center)
				.HAlign(HAlign_Left)

				+ SHorizontalBox::Slot()
				.FillWidth(1.0f)
				.HAlign(HAlign_Right)
				[
					SNew(SUniformGridPanel)
					.SlotPadding(FMargin(4.0f, 0.0f, 0.0f, 0.0f))
					+ SUniformGridPanel::Slot(1, 0)
					[
						SNew(SButton)
						.HAlign(HAlign_Center)
						.Text(TypeVal.Equals("REBASE") ? LOCTEXT("ContinueRebase", "Continue Rebase") : LOCTEXT("ContinueMerge", "Continue Merge"))
						.OnClicked(this, &SRebaseWindow::OnContinueClick, TypeVal)
						.Visibility(((TypeVal.Equals("REBASE") || TypeVal.Equals("MERGE")) && Num <= 0) ? EVisibility::Visible : EVisibility::Collapsed)
					]
					+ SUniformGridPanel::Slot(2, 0)
					[
						SNew(SButton)
						.HAlign(HAlign_Center)
						.Text(TypeVal.Equals("REBASE") ? LOCTEXT("Abort Rebase", "Abort Rebase") : LOCTEXT("Abort Merge", "Abort Merge"))
						.OnClicked(this, &SRebaseWindow::OnAbortClick, TypeVal)
						.Visibility(TypeVal.Equals("REBASE") || TypeVal.Equals("MERGE") ? EVisibility::Visible : EVisibility::Collapsed)
					]
					+ SUniformGridPanel::Slot(3, 0)
					[
						SNew(SButton)
						.HAlign(HAlign_Center)
						.Text(LOCTEXT("CloseWindowMerge", "Close"))
						.OnClicked(this, &SRebaseWindow::OnCloseClick)
					]
				]
				]
		]
		];
	
}

TSharedRef<ITableRow> SRebaseWindow::OnGenerateRowForList(TSharedPtr<FRebaseItem> SubmitItem, const TSharedRef<STableViewBase>& OwnerTable)
{
	TSharedRef<ITableRow> Row =
		SNew(SRebaseWindowListRow, OwnerTable)
		.SRebaseWindow(SharedThis(this))
		.Item(SubmitItem)
		.IsEnabled(true);

	return Row;
}

TSharedRef<SWidget> SRebaseWindow::GenerateWidgetForItemAndColumn(TSharedPtr<FRebaseItem> Item, const FName ColumnID) const
{
	bool IsConflict = Item->IsConflict();

	bool bCanExecuteMerge = false;

	if (IsConflict)
	{
		FString V = FRebaseItem::ConvertPackagePathToAssetPath(Item->RebaseFilePath);
		FAssetToolsModule& AssetToolsModule = FModuleManager::GetModuleChecked<FAssetToolsModule>("AssetTools");

		IAssetRegistry& AssetRegistry = FModuleManager::LoadModuleChecked<FAssetRegistryModule>(TEXT("AssetRegistry")).Get();
#if ENGINE_MAJOR_VERSION == 5 && ENGINE_MINOR_VERSION >= 1
		FAssetData AssetData = AssetRegistry.GetAssetByObjectPath(FSoftObjectPath(V));
#else
		FAssetData AssetData = AssetRegistry.GetAssetByObjectPath(*V);
#endif

		UObject* CurrentObject = AssetData.GetAsset();

		if (CurrentObject)
		{
			auto AssetTypeActions = AssetToolsModule.Get().GetAssetTypeActionsForClass(CurrentObject->GetClass()).Pin();
			if (AssetTypeActions.IsValid())
			{
				bCanExecuteMerge = AssetTypeActions->CanMerge();
			}
		}
	}

	check(Item.IsValid());

	const FMargin RowPadding(8.0f, 8.0f, 8.0f, 8.0f);

	TSharedPtr<SWidget> ItemContentWidget;

	if (ColumnID == SRebaseWindowDefs::ColumnID_ResolveConflict)
	{
		ItemContentWidget = SNew(SHorizontalBox)
			+ SHorizontalBox::Slot()
			.Padding(RowPadding)
			[
				SNew(SButton)
				.HAlign(HAlign_Center)
				.Text(LOCTEXT("ResolveConflict", "Resolve Conflict"))
				.OnClicked(Item.Get(), &FRebaseItem::OnResolveConflictClick)
				.IsEnabled(bCanExecuteMerge)
			];
	}
	else if (ColumnID == SRebaseWindowDefs::ColumnID_CheckBox)
	{
		Item->bCheckBoxStatus = false;
		ItemContentWidget = SNew(SHorizontalBox)
			+ SHorizontalBox::Slot()
			.Padding(RowPadding)
			[
				SNew(SCheckBox)
				.IsChecked_Lambda([Item]() {
					return Item->bCheckBoxStatus ? ECheckBoxState::Checked : ECheckBoxState::Unchecked;
				})
				.OnCheckStateChanged(Item.Get(), &FRebaseItem::OnCheckCheckBox)
			];
	}
	else if (ColumnID == SRebaseWindowDefs::ColumnID_UseMine)
	{
		ItemContentWidget = SNew(SHorizontalBox)
			+ SHorizontalBox::Slot()
			.Padding(RowPadding)
			[
				SNew(SButton)
				.HAlign(HAlign_Center)
				.Text(LOCTEXT("UseMine", "Use branch version"))
				.OnClicked(Item.Get(), &FRebaseItem::OnBranchClick, TypeVal)
				.IsEnabled(IsConflict)
			];
	}
	else if (ColumnID == SRebaseWindowDefs::ColumnID_UseTheirs)
	{
		ItemContentWidget = SNew(SHorizontalBox)
			+ SHorizontalBox::Slot()
			.Padding(RowPadding)
			[
				SNew(SButton)
				.HAlign(HAlign_Center)
				.Text(LOCTEXT("UseTheirs", "Use [Stash] version"))
				.OnClicked(Item.Get(), &FRebaseItem::OnStashClick, TypeVal)
				.IsEnabled(IsConflict)
			];
	}
	/*else if (ColumnID == SRebaseWindowDefs::ColumnID_RebaseFilePath)
	{
		ItemContentWidget = SNew(SHorizontalBox)
			+ SHorizontalBox::Slot()
			.HAlign(HAlign_Center)
			.VAlign(VAlign_Center)
			[
				SNew(SImage)
	#if ENGINE_MAJOR_VERSION == 5 && ENGINE_MINOR_VERSION >= 1
				.Image(FAppStyle::GetBrush(Item->GetIconName()))
	#else
				.Image(FEditorStyle::GetBrush(Item->GetIconName()))
	#endif
			.ToolTipText(Item->GetIconTooltip())
			];
	}*/
	else if (ColumnID == SRebaseWindowDefs::ColumnID_RebaseFilePath)
	{
#if ENGINE_MAJOR_VERSION >= 5
		FString Path = GitSourceControlUtils::GetOFPAPathOrFilePath(Item->GetDisplayName().ToString());
#else
		FString Path = Item->GetDisplayName().ToString();
#endif
		ItemContentWidget = SNew(SHorizontalBox)
			+ SHorizontalBox::Slot()
			.Padding(RowPadding)
			[
				SNew(STextBlock)
				.Text(FText::FromString(Path))
				.ToolTipText(Item->GetDisplayName())
			];
	}

	return ItemContentWidget.ToSharedRef();
}


void SRebaseWindowListRow::Construct(const FArguments& InArgs, const TSharedRef<STableViewBase>& InOwnerTableView)
{
	SRebaseWindowPtr = InArgs._SRebaseWindow;
	Item = InArgs._Item;

	SMultiColumnTableRow<TSharedPtr<FRebaseItem>>::Construct(FSuperRowType::FArguments(), InOwnerTableView);
}


TSharedRef<SWidget> SRebaseWindowListRow::GenerateWidgetForColumn(const FName& ColumnName)
{
	// Create the widget for this item
	TSharedPtr<SRebaseWindow> SRebaseWindow = SRebaseWindowPtr.Pin();
	if (SRebaseWindow.IsValid())
	{
		return SRebaseWindow->GenerateWidgetForItemAndColumn(Item, ColumnName);
	}

	// Packages dialog no longer valid; return a valid, null widget.
	return SNullWidget::NullWidget;
}

#undef LOCTEXT_NAMESPACE
