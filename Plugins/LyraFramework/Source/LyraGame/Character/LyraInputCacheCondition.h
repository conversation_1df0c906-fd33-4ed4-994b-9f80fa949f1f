#pragma once

#include "GameplayTagContainer.h"
#include "LyraInputCacheCondition.generated.h"

USTRUCT(BlueprintType)
struct FLyraInputCacheCondition
{
	GENERATED_USTRUCT_BODY()

	//检测条件的Tag
	UPROPERTY(BlueprintReadWrite, EditDefaultsOnly, Meta = (Categories = "InputTag"))
	FGameplayTagContainer TargetTagContainer;

	//需要满足的条件
	UPROPERTY(BlueprintReadWrite, EditDefaultsOnly)
	FGameplayTagQuery RequireQuery;
};
