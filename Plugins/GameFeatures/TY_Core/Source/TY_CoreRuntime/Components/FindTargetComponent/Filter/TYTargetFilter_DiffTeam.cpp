// Fill out your copyright notice in the Description page of Project Settings.


#include "TYTargetFilter_DiffTeam.h"

#include "Teams/LyraTeamSubsystem.h"

bool UTYTargetFilter_DiffTeam::FilterCheck_Implementation(AActor* Caller, AActor* Target) const
{
	if (!IsValid(Caller) || !IsValid(Target))
		return false;

	ULyraTeamSubsystem* LyraTeamSubsystem =  Caller->GetWorld()->GetSubsystem<ULyraTeamSubsystem>();
	ELyraTeamComparison LyraTeamComparison = LyraTeamSubsystem->CompareTeams(Caller, Target);

	return LyraTeamComparison == ELyraTeamComparison::DifferentTeams;
}
