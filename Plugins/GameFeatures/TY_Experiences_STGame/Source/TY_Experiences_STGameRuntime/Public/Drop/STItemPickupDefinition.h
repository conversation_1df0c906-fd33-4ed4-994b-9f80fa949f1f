// Fill out your copyright notice in the Description page of Project Settings.

#pragma once


#include "AbilitySystemGlobals.h"
#include "AbilitySystem/LyraAbilitySet.h"
#include "CoreMinimal.h"
#include "Equipment/LyraPickupDefinition.h"
#include "STItemPickupDefinition.generated.h"


class UGameplayEffect;

UCLASS(Blueprintable)
class TY_EXPERIENCES_STGAMERUNTIME_API USTItemPickupDefinition : public ULyraPickupDefinition
{
	GENERATED_BODY()
	
public:

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lyra|Pickup");
	FString Name;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lyra|Pickup");
	FString Description;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lyra|Pickup");
	int LevelItem;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lyra|Pickup");
	int LevelRequirement;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Lyra|Pickup")
	TArray<TObjectPtr<ULyraAbilitySet>> AbilitySets;

	UFUNCTION(BlueprintCallable, Category = "Lyra|Pickup")
	void ApplyAbilities(AActor* OwningActor);
};
