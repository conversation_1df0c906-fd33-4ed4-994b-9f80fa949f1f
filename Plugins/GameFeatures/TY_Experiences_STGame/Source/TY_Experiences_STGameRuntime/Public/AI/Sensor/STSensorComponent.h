// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameplayTagContainer.h"
#include "Components/ActorComponent.h"
#include "STSensorComponent.generated.h"


class ASTCollisionSensor;

UCLASS(ClassGroup=(ST), meta=(BlueprintSpawnableComponent))
class TY_EXPERIENCES_STGAMERUNTIME_API USTSensorComponent : public UActorComponent
{
	GENERATED_BODY()
public:
	USTSensorComponent();

	UFUNCTION(BlueprintPure, Category = "ST|AI", Meta=(DefaultToSelf = "Actor"))
	static USTSensorComponent* GetSTSensorComponent(AActor* Actor);
	
	virtual void TickComponent(float DeltaTime, ELevelTick TickType,
	                           FActorComponentTickFunction* ThisTickFunction) override;

	UFUNCTION(BlueprintCallable)
	ASTCollisionSensor* SpawnSensor(TSubclassOf<ASTCollisionSensor> SensorClass, FGameplayTag InSensorTag);

	UFUNCTION(BlueprintCallable)
	void RemoveSensor(ASTCollisionSensor* InSensor);
	
	UFUNCTION(BlueprintCallable)
	void RemoveSensorByTag(FGameplayTag InSensorTag);

protected:
	virtual void BeginPlay() override;

public:
	UPROPERTY()
	TArray<TObjectPtr<ASTCollisionSensor>> AllSensors;
};
