// Copyright (C) Developed by Pask, Published by Dark Tower Interactive SRL 2023. All Rights Reserved. 

#pragma once

#include "CoreMinimal.h"
#include "Engine/DeveloperSettings.h"
#include "STDevelopSettings.generated.h"

/**
 * 
 */
UCLASS(Config=Game, DefaultConfig, meta=(DisplayName="ST开发者选项"))
class TY_EXPERIENCES_STGAMERUNTIME_API USTDevelopSettings : public UDeveloperSettings
{
	GENERATED_BODY()

public:
	UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "技能", meta=(DisplayName="是否显示技能攻击范围"))
	bool EnableDrawAttackBoxOfMontage = false;

	UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "技能", meta=(DisplayName="是否显示攻击预警范围"))
	bool EnableDrawAttackWarningBoxOfMontage = false;

	UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "技能", meta=(DisplayName="是否绘制TargetWarp目标"))
	bool EnableDrawTargetWarpTick = false;


	UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "AI", meta=(DisplayName="打印当前释放的技能"))
	bool EnableDrawCurrentSkillDebugInfo = false;

	UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "AI", meta=(DisplayName="允许强制技能释放，需在对应的AIController中勾选"))
	bool EnableForceSkillSpell = false;


	UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "动画", meta=(DisplayName="打印旋转动画信息"))
	bool EnableDrawRotationInfo = false;

	UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "动画", meta=(DisplayName="避免AI释放技能"))
	bool DisableAICastSpell = false;

	
	UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "操作", meta=(DisplayName="显示鼠标位置"))
	bool EnableDrawMouseLocation = false;

	UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Mass", meta=(DisplayName="显示Mass生成点"))
	bool EnableDrawMassFormationPoints = false;
	
	UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "AI", meta=(DisplayName="显示占用位置"))
	bool EnableDrawOccupiedAttackLocation = false;

	UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "AI", meta=(DisplayName="分配网格边长"))
	int AttackLocationRadius = 150;

	UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "LevelSequencer")
	bool bEnableLevelSequencer;
};