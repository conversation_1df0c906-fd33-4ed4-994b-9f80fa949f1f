// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "MassEntitySpawnDataGeneratorBase.h"
#include "ZoneGraphData.h"
#include "Mass/Processor/STFormationInitDataProcessor.h"
#include "Mass/Shape/STFormationShapeComponent.h"
#include "STPolygonEdgeGenerator.generated.h"


UENUM(BlueprintType)
enum ESTDirectionType : int
{
	FollowFormation = 0 UMETA(DisplayName = "跟随军阵朝向"), // 跟随军阵朝向
	FaceOut = 1 UMETA(DisplayName = "朝外"), // 朝外
	FaceIn = 2 UMETA(DisplayName = "朝内"), // 朝内
};

/**
 * 在一个多边形的边缘生成单位
 */
UCLASS()
class TY_EXPERIENCES_STGAMERUNTIME_API USTPolygonEdgeGenerator : public UMassEntitySpawnDataGeneratorBase
{
	GENERATED_BODY()
	
public:
	virtual void Generate(UObject &QueryOwner, TConstArrayView<FMassSpawnedEntityType> EntityTypes, int32 Count, FFinishedGeneratingSpawnDataSignature &FinishedGeneratingSpawnPointsDelegate) const override;

	
	virtual void Generate2(UObject &QueryOwner, TConstArrayView<FMassSpawnedEntityType> EntityTypes, int32 Count, FFinishedGeneratingSpawnDataSignature &FinishedGeneratingSpawnPointsDelegate) const;

	void BuildResultsFromEntityTypes(const int32 SpawnCount, TConstArrayView<FMassSpawnedEntityType> EntityTypes, TArray<FMassEntitySpawnDataGeneratorResult>& OutResults) const;

	void GeneratePointsForZoneGraphData(const ::AZoneGraphData &ZoneGraphData, const FZoneData& ZoneData, TArray<FTransform> &Locations, TArray<int>& ZoneIds, TArray<TArray<int>>& BelongLineDatas) const;
	
	void GeneratePointsForOneShape(const USTFormationShapeComponent* FormationShape, TArray<FTransform> &Locations, TArray<int>& ZoneIds, TArray<TArray<FSTFormationLineData>>& BelongLineDatas) const;

	FQuat CalculateFaceQuat(const USTFormationShapeComponent* ShapeComponent, const FVector& CurPoint, const FVector& NextPoint, bool isLastPoint) const;
	
	
	UPROPERTY(EditAnywhere, Category = "Generator Config", DisplayName="单位半径")
	float EntityRadius;

	UPROPERTY(EditAnywhere, Category = "Generator Config", DisplayName="最后一个单位的半径")
	float LastOneAllowRadius;
	
	UPROPERTY(EditAnywhere, Category = "Generator Config")
	FZoneGraphTagFilter TagFilter;

	UPROPERTY(EditAnywhere, Category = "Generator Config", DisplayName="朝向配置")
	TEnumAsByte<ESTDirectionType> DirectionType;
};
