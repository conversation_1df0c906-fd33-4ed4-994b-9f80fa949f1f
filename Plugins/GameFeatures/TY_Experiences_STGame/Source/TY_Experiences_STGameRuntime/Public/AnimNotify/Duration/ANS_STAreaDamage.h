// Copyright (C) Developed by Pask, Published by Dark Tower Interactive SRL 2023. All Rights Reserved. 

#pragma once

#include "CoreMinimal.h"
#include "AnimNotify/STAnimNotifyDuration.h"
#include "Weapon/Damage/STAreaDamageInfo.h"
#include "ANS_STAreaDamage.generated.h"

/**
 * 
 */
UCLASS()
class TY_EXPERIENCES_STGAMERUNTIME_API UANS_STAreaDamage : public USTAnimNotifyDuration
{
	GENERATED_BODY()

public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "ST")
	FSTAreaDamageInfo DamageInfo;
	
public:
#if WITH_EDITOR
	virtual void PostEditChangeProperty(struct FPropertyChangedEvent& PropertyChangedEvent) override;
#endif
	
protected: 
	virtual void OnBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration, const FAnimNotifyEventReference& EventReference) override;
	virtual void OnEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
private:

};
