#pragma once

#include "GameplayTagContainer.h"
#include "AbilitySystem/STSkillTypes.h"
#include "STDamageDesc.generated.h"


USTRUCT(BlueprintType)
struct FSTDamageDesc
{
	GENERATED_USTRUCT_BODY()

	/*-----------------------------------------------技能上的静态配置开始（可抽出为独立配置）--------------------------*/
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float OriginDamage;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "ST")
	float StunIncrease;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int StiffLevel;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FGameplayTag DamageTag;

	//攻击者的攻击方向
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TEnumAsByte<EAttackDirection> AttackDirection;

	/*-----------------------------------------------技能上的静态配置结束--------------------------*/
	
	/*-----------------------------------------------运行时计算的结果开始--------------------------*/
	
	UPROPERTY(BlueprintReadWrite)
	FVector HitPoint;

	//受击者的受击方向
	UPROPERTY(BlueprintReadWrite)
	TEnumAsByte<EBeHitDirection> BeHitDirection;

	/*-----------------------------------------------运行时计算的结果结束--------------------------*/
};
