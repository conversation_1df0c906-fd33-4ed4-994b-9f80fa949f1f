// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Engine/DataAsset.h"

#include "STDeadAsset.generated.h"

class ASTEnemy;
/**
 * 
 */
UCLASS()
class TY_EXPERIENCES_STGAMERUNTIME_API USTDeadAsset : public UDataAsset
{
	GENERATED_BODY()

public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "ST", DisplayName = "下一阶段")
	TSubclassOf<ASTEnemy> NextStageBoss = nullptr;

	UPROPERTY(EditAnywhere,BlueprintReadWrite,Category = "ST",DisplayName = "是否拥有下一阶段")
	bool bHasNextStage = false;
	
};
