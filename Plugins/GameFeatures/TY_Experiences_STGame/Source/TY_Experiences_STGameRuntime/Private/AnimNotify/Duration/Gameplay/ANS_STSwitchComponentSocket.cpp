// Fill out your copyright notice in the Description page of Project Settings.


#include "AnimNotify/Duration/Gameplay/ANS_STSwitchComponentSocket.h"

#include "AbilitySystem/Component/STCommonGameplayComponent.h"

void UANS_STSwitchComponentSocket::OnBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
                                           float TotalDuration, const FAnimNotifyEventReference& EventReference)
{
	Super::OnBegin(MeshComp, Animation, TotalDuration, EventReference);

	auto GameplayComponent = USTCommonGameplayComponent::GetGameplayComponent(MeshComp);
	if (GameplayComponent == nullptr)
	{
		return;
	}

	auto Instance = GameplayComponent->CreateUniqueStruct(this->GetUniqueID(), this->SwitchComponentSocket);
	Instance->OnBegin(MeshComp);
}

void UANS_STSwitchComponentSocket::OnEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	auto GameplayComponent = USTCommonGameplayComponent::GetGameplayComponent(MeshComp);
	if (GameplayComponent == nullptr)
	{
		return;
	}

	auto Instance = GameplayComponent->GetUniqueStruct<FSTSwitchComponentSocket>(this->GetUniqueID());
	if (Instance.IsValid())
	{
		Instance->OnEnd(MeshComp);
	}

	GameplayComponent->DestroyUniqueStruct(this->GetUniqueID());
	
	Super::OnEnd(MeshComp, Animation, EventReference);
}

