// Copyright (C) Developed by Pask, Published by Dark Tower Interactive SRL 2023. All Rights Reserved. 


#include "AnimNotify/Duration/SetGlobalTimeDilationDuration.h"

#include "Kismet/GameplayStatics.h"

void USetGlobalTimeDilationDuration::OnBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
                                             float TotalDuration, const FAnimNotifyEventReference& EventReference)
{
	Super::OnBegin(MeshComp, Animation, TotalDuration, EventReference);
	UGameplayStatics::SetGlobalTimeDilation(MeshComp, this->TimeDilationFactor);
}

void USetGlobalTimeDilationDuration::OnEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::OnEnd(MeshComp, Animation, EventReference);
	UGameplayStatics::SetGlobalTimeDilation(MeshComp, 1.0f);
}
