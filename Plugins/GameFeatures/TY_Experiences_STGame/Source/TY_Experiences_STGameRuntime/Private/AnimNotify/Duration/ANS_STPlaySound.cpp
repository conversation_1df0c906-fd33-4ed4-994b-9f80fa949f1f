// Fill out your copyright notice in the Description page of Project Settings.


#include "AnimNotify/Duration/ANS_STPlaySound.h"

#include "Audio/STAudioExtensionComponent.h"
#include "Common/STCommonFunctionLibrary.h"
#include "Components/AudioComponent.h"
#include "Kismet/GameplayStatics.h"

#if WITH_EDITOR
#include "Logging/MessageLog.h"
#include "Misc/UObjectToken.h"
#endif


UANS_STPlaySound::UANS_STPlaySound()
{
	VolumeMultiplier = 1.f;
	PitchMultiplier = 1.f;

#if WITH_EDITORONLY_DATA
	NotifyColor = FColor(196, 142, 255, 255);
	bPreviewIgnoreAttenuation = false;
#endif // WITH_EDITORONLY_DATA
}

#if WITH_EDITOR
void UANS_STPlaySound::ValidateAssociatedAssets()
{
	static const FName NAME_AssetCheck("AssetCheck");

	if (Sound != nullptr && !Sound->IsOneShot())
	{
		UObject* ContainingAsset = GetContainingAsset();

		FMessageLog AssetCheckLog(NAME_AssetCheck);

		const FText MessageLooping = FText::Format(
			NSLOCTEXT("AnimNotify", "Sound_ShouldNotLoop", "Sound {0} used in anim notify for asset {1} is set to looping, but the slot is a one-shot (it won't be played to avoid leaking an instance per notify)."),
			FText::AsCultureInvariant(Sound->GetPathName()),
			FText::AsCultureInvariant(ContainingAsset->GetPathName()));
		AssetCheckLog.Warning()
			->AddToken(FUObjectToken::Create(ContainingAsset))
			->AddToken(FTextToken::Create(MessageLooping));

		if (GIsEditor)
		{
			AssetCheckLog.Notify(MessageLooping, EMessageSeverity::Warning, /*bForce=*/ true);
		}
	}
}
#endif

void UANS_STPlaySound::OnBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
	const FAnimNotifyEventReference& EventReference)
{
	if (!IsValid(MeshComp))
	{
		return;
	}
	Super::OnBegin(MeshComp, Animation, TotalDuration, EventReference);

	UAudioComponent* AudioComp = nullptr;
	
	if (Sound)
	{
		if (!Sound->IsOneShot())
		{
			UE_LOG(LogAudio, Warning, TEXT("PlaySound notify: Anim %s tried to play a sound asset which is not a one-shot: '%s'. Spawning suppressed."), *GetNameSafe(Animation), *GetNameSafe(Sound));
			return;
		}

#if WITH_EDITORONLY_DATA
		UWorld* World = MeshComp->GetWorld();
		if (bPreviewIgnoreAttenuation && World && World->WorldType == EWorldType::EditorPreview)
		{
			if (MeshComp->IsPlaying())
			{
				AudioComp = UGameplayStatics::SpawnSound2D(World, Sound, VolumeMultiplier, PitchMultiplier);
			}
		}
		else
#endif
		{
			if (bFollow)
			{
				AudioComp = UGameplayStatics::SpawnSoundAttached(Sound, MeshComp, AttachName, FVector(ForceInit), EAttachLocation::SnapToTarget, false, VolumeMultiplier, PitchMultiplier);
			}
			else
			{
				AudioComp = UGameplayStatics::SpawnSoundAtLocation(MeshComp->GetWorld(), Sound, MeshComp->GetComponentLocation(), FRotator::ZeroRotator, VolumeMultiplier, PitchMultiplier);
			}
		}
	}

	if (AudioComp && MeshComp->GetOwner())
	{
		USTAudioExtensionComponent* AudioExtensionComp = MeshComp->GetOwner()->GetComponentByClass<USTAudioExtensionComponent>();
		if (USTCommonFunctionLibrary::IsPreviewScene(MeshComp) && AudioExtensionComp == nullptr)
		{
			AudioExtensionComp = Cast<USTAudioExtensionComponent>(MeshComp->GetOwner()->AddComponentByClass(
				USTAudioExtensionComponent::StaticClass(), true, FTransform(), true));
		}

		if (AudioExtensionComp != nullptr)
		{
			AudioExtensionComp->RegisterAudioComponent(this->GetUniqueID(), AudioComp);
		}

		this->K2_OnSpawnAudio(AudioComp);
	}
}

void UANS_STPlaySound::OnEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	if (!IsValid(MeshComp->GetOwner()))
	{
		return;
	}
	USTAudioExtensionComponent* AudioExtensionComp = MeshComp->GetOwner()->GetComponentByClass<USTAudioExtensionComponent>();
	if (AudioExtensionComp != nullptr)
	{
		if (auto AudioComp = AudioExtensionComp->GetAudioComponent(this->GetUniqueID()))
		{
			if (AudioComp != nullptr && AudioComp->IsPlaying())
			{
				if (bForceDestroyOnEnd)
				{
					AudioComp->Stop();
					AudioComp = nullptr;
				}
				if (AudioComp != nullptr)
				{
					this->K2_OnStateEnd(AudioComp);
				}
			}
		}
		
		AudioExtensionComp->UnregisterAudioComponent(this->GetUniqueID());
	}
	
	Super::OnEnd(MeshComp, Animation, EventReference);
}


