// Fill out your copyright notice in the Description page of Project Settings.


#include "Weapon/Behit/STBeHitComponent.h"

#include "AbilitySystemBlueprintLibrary.h"
#include "AbilitySystem/STAbilitySystemBlueprintLibrary.h"
#include "AbilitySystem/STGameplayTags.h"
#include "AbilitySystem/Component/AttackFeedbackComponent.h"
#include "AbilitySystem/Component/STDodgeComponent.h"
#include "AbilitySystem/Component/TTTBComponent.h"
#include "Common/STCommonFunctionLibrary.h"
#include "GameFramework/GameplayMessageSubsystem.h"
#include "Kismet/GameplayStatics.h"


// Sets default values for this component's properties
USTBeHitComponent::USTBeHitComponent()
{
	// Set this component to be initialized when the game starts, and to be ticked every frame.  You can turn these features
	// off to improve performance if you don't need them.
	PrimaryComponentTick.bCanEverTick = true;
}


// Called when the game starts
void USTBeHitComponent::BeginPlay()
{
	Super::BeginPlay();
	this->LastBeHitTime = UGameplayStatics::GetTimeSeconds(this);
	UGameplayMessageSubsystem& MessageSubsystem = UGameplayMessageSubsystem::Get(this);
	ListenerHandle = MessageSubsystem.RegisterListener(STGameplayTags::Message_Damage, this, &ThisClass::OnDamageMessage);
}

void USTBeHitComponent::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
	UGameplayMessageSubsystem& MessageSubsystem = UGameplayMessageSubsystem::Get(this);
	MessageSubsystem.UnregisterListener(ListenerHandle);
	
	Super::EndPlay(EndPlayReason);
}

void USTBeHitComponent::OnDamageMessage(FGameplayTag Channel, const FSTDamageMessage& Payload)
{
	//受击者是我
	if (Payload.Victim != this->GetOwner())
	{
		return;
	}
	
	auto AbilityExtensionComponent = this->GetOwner()->GetComponentByClass<USTAbilityExtensionComponent>();
	if (AbilityExtensionComponent == nullptr)
	{
		return;
	}

	this->SetLastBeHitTime(UGameplayStatics::GetTimeSeconds(this));

	auto AttackerCharacter = Payload.Attacker;
	auto VictimASC = UAbilitySystemBlueprintLibrary::GetAbilitySystemComponent(this->GetOwner());
	
	bool InTTTB = VictimASC->HasMatchingGameplayTag(STGameplayTags::State_InTTTB);
	auto TTTBComponent = this->GetOwner()->GetComponentByClass<UTTTBComponent>();
	if (InTTTB && TTTBComponent != nullptr)
	{
		TTTBComponent->OnCastTTTB(AttackerCharacter);
	}

	auto AttackerAttackFeedbackComponent = AttackerCharacter->GetComponentByClass<UAttackFeedbackComponent>();
	if (AttackerAttackFeedbackComponent != nullptr)
	{
		AttackerAttackFeedbackComponent->OnAttackFeedback(Payload.Victim);
	}

	if (Payload.Damage > 0.0f)
	{
		auto DodgeComponent = Payload.Victim->GetComponentByClass<USTDodgeComponent>();
		if (DodgeComponent != nullptr)
		{
			DodgeComponent->OnBeHit();
		}
	}

	if (const auto attributeSet = Cast<USTCommonAttributeSet>(Payload.Victim->GetAttributeSet(USTCommonAttributeSet::StaticClass())))
	{
		// TODO 
		// auto ActionManager = Attacker->GetComponentByClass<USTActionsManagerComponent>();
		// if (ActionManager != nullptr)
		// {
		// 	ActionManager->MarkHitTarget();
		// }

		FSTHitEvent HitEvent;
		HitEvent.Attacker = Payload.Attacker;
		HitEvent.Victim = this->GetOwner();
		HitEvent.FinalDamage = Payload.Damage;
		HitEvent.BeHitDirection = USTCommonFunctionLibrary::GetBeHitDirection(Payload.Attacker, Payload.Victim);

		if (VictimASC->HasMatchingGameplayTag(STGameplayTags::State_ImmueStiff))
		{
			HitEvent.FinalStiff = -1;
		}
		else
		{
			HitEvent.FinalStiff = Payload.StiffLevel > attributeSet->GetTenacity() ? Payload.StiffLevel : -1;
		}
		
		USTAbilitySystemBlueprintLibrary::SendSTHitEvent(Payload.Attacker, STGameplayTags::Actions_MakeDamage, HitEvent);

		this->LastBeHitEvent = HitEvent;
		float CurrentHealth = attributeSet->GetHealth();
		if (CurrentHealth <= 0.f)
		{
			if (auto DeadComponent = Payload.Victim->GetComponentByClass<USTDeadComponent>())
			{
				DeadComponent->SetDeadState(EDS_Dying);
			}
			USTAbilitySystemBlueprintLibrary::SendSTHitEvent(this->GetOwner(), STGameplayTags::Actions_Dead, HitEvent);
		}
		else
		{
			USTAbilitySystemBlueprintLibrary::SendSTHitEvent(this->GetOwner(), STGameplayTags::Actions_BeHit, HitEvent);
		}
	}

	if (InTTTB && TTTBComponent)
	{
		TTTBComponent->OnCastTTTBEnd();
	}
}


// Called every frame
void USTBeHitComponent::TickComponent(float DeltaTime, ELevelTick TickType,
                                      FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
}

void USTBeHitComponent::SetSpecialBeHitConfig(FBeHitConfig& BeHitConfig)
{
	this->bHasSpecialBeHitConfig = true;
	this->SpecialBeHitConfig = BeHitConfig;
}

void USTBeHitComponent::ClearSpecialBeHitConfig()
{
	this->bHasSpecialBeHitConfig = false;
	this->SpecialBeHitConfig = FBeHitConfig();
}
