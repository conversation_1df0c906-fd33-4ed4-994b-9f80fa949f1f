// Fill out your copyright notice in the Description page of Project Settings.


#include "AI/Patrol/STAIPatrolComponent.h"
#include "AI/Patrol/STPatrolPath.h"


// Sets default values for this component's properties
USTAIPatrolComponent::USTAIPatrolComponent()
{
	// Set this component to be initialized when the game starts, and to be ticked every frame.  You can turn these features
	// off to improve performance if you don't need them.
	PrimaryComponentTick.bCanEverTick = true;

	// ...
}


// Called when the game starts
void USTAIPatrolComponent::BeginPlay()
{
	Super::BeginPlay();
}

// Called every frame
void USTAIPatrolComponent::TickComponent(float DeltaTime, ELevelTick TickType,
                                         FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

	if (CurrentPatrolPath != nullptr)
	{
		this->CurrentPatrolPath->TickPatrol(DeltaTime);
	}
}

void USTAIPatrolComponent::BeginPatrol(TSubclassOf<ASTPatrolPath> InPatrolPath)
{
	if (InPatrolPath == nullptr)
	{
		return;
	}

	EndPatrol();
	this->CurrentPatrolPath = Cast<ASTPatrolPath>(this->GetWorld()->SpawnActor(InPatrolPath));
	if (this->CurrentPatrolPath == nullptr)
	{
		return;
	}

	//刷到当前角色位置上
	this->CurrentPatrolPath->SetActorTransform(this->GetOwner()->GetTransform());
	this->CurrentPatrolPath->BeginPatrol(Cast<APawn>(this->GetOwner()));
}

void USTAIPatrolComponent::EndPatrol()
{
	if (this->CurrentPatrolPath == nullptr)
	{
		return;
	}

	this->CurrentPatrolPath->EndPatrol();
	this->CurrentPatrolPath->Destroy();
	this->CurrentPatrolPath = nullptr;
}

