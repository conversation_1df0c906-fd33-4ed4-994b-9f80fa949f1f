// Fill out your copyright notice in the Description page of Project Settings.


#include "AI/GroupAI/STGroupAIFunctionLibrary.h"

#include "Actor/STCharacterBase.h"
#include "AI/Controller/STAIController.h"
#include "AI/GroupAI/Formation/Spawner/STFormationEnemySpawnInfo.h"

void USTGroupAIFunctionLibrary::EnableAICrowdSimulation(AActor* InActor)
{
	auto* Pawn = Cast<APawn>(InActor);
	if(Pawn == nullptr)
	{
		return;
	}

	auto AIController = Pawn->Controller;
	if(AIController == nullptr)
	{
		return;
	}

	UCrowdFollowingComponent* CrowdFollowingComponent = AIController->GetComponentByClass<UCrowdFollowingComponent>();
	if(CrowdFollowingComponent == nullptr)
	{
		return;
	}

	CrowdFollowingComponent->SetCrowdSimulationState(ECrowdSimulationState::Enabled);
}

void USTGroupAIFunctionLibrary::DisableAICrowdSimulation(AActor* InActor)
{
	auto Pawn = Cast<APawn>(InActor);
	if(Pawn == nullptr)
	{
		return;
	}

	auto AIController = Pawn->Controller;
	if(AIController == nullptr)
	{
		return;
	}

	UCrowdFollowingComponent* CrowdFollowingComponent = AIController->GetComponentByClass<UCrowdFollowingComponent>();
	if(CrowdFollowingComponent == nullptr)
	{
		return;
	}

	CrowdFollowingComponent->SetCrowdSimulationState(ECrowdSimulationState::Disabled);
}

FVector USTGroupAIFunctionLibrary::GetCentroid(const TArray<FSTFormationEnemySpawnInstance>& EnemyInstances)
{
	FVector Centroid = FVector::Zero();

	int TotalNum = 0;
	for (auto& EnemyInstance : EnemyInstances)
	{
		if (!EnemyInstance.SpawnCharacter.IsValid())
		{
			continue;
		}
		Centroid += EnemyInstance.SpawnCharacter.Get()->GetActorLocation();
		TotalNum++;
	}

	Centroid /= TotalNum;
	return Centroid;
}

FSTFormationReorganizeHandle USTGroupAIFunctionLibrary::GenerateReorganizeHandle()
{
	static int32 GHandleID=0;
	FSTFormationReorganizeHandle NewHandle(GHandleID++);

	return NewHandle;
}

bool USTGroupAIFunctionLibrary::ReorganizeHandleIsValid(FSTFormationReorganizeHandle InHandle)
{
	return InHandle.IsValid();
}
