// Copyright (C) Developed by <PERSON><PERSON>, Published by Dark Tower Interactive SRL 2023. All Rights Reserved. 


#include "AbilitySystem/Component/STCommonGameplayComponent.h"

#include "NiagaraComponent.h"

void USTCommonGameplayComponent::BeginPlay()
{
	Super::BeginPlay();
}

void USTCommonGameplayComponent::TickComponent(float DeltaTime, ELevelTick TickType,
	FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
}

void USTCommonGameplayComponent::AddFakeAttachEffects(UNiagaraComponent* InComponent)
{
	InComponent->OnSystemFinished.AddUniqueDynamic(this, &USTCommonGameplayComponent::OnNiagaraSystemFinished);
	this->FakeAttachEffects.Add(InComponent);
}

void USTCommonGameplayComponent::SetFakeAttachEffectsTimeDilation(float InTimeDilation)
{
	for (auto NiagaraComponent : this->FakeAttachEffects)
	{
		if (NiagaraComponent == nullptr)
		{
			continue;
		}
		NiagaraComponent.Get()->SetCustomTimeDilation(InTimeDilation);
	}
}

void USTCommonGameplayComponent::RegisterAnimNotifyObject(int UniqueID, UObject* AnimNotifyObject)
{
	this->CacheAnimNotifyObjects.Add(UniqueID, AnimNotifyObject);
}

UObject* USTCommonGameplayComponent::GetAnimNotifyObject(int UniqueID)
{
	auto OutObject = this->CacheAnimNotifyObjects.Find(UniqueID);
	if (OutObject == nullptr)
	{
		return nullptr;
	}
	
	return OutObject->Get();
}

void USTCommonGameplayComponent::UnregisterAnimNotifyObject(int UniqueID)
{
	this->CacheAnimNotifyObjects.Remove(UniqueID);
}

USTCommonGameplayComponent* USTCommonGameplayComponent::GetGameplayComponent(const USkeletalMeshComponent* MeshComp)
{
	if (MeshComp == nullptr || MeshComp->GetOwner() == nullptr)
	{
		return nullptr;
	}

	return MeshComp->GetOwner()->GetComponentByClass<USTCommonGameplayComponent>();
}

void USTCommonGameplayComponent::OnNiagaraSystemFinished(UNiagaraComponent* FinishedComponent)
{
	this->FakeAttachEffects.Remove(FinishedComponent);
}

