// Copyright (C) Developed by <PERSON><PERSON>, Published by Dark Tower Interactive SRL 2023. All Rights Reserved. 


#include "AbilitySystem/STGameplayTags.h"

#include "NativeGameplayTags.h"

namespace STGameplayTags
{
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Team_PlayerTeam, "STGame.Team.Player", "Player Team Root");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Team_EnemyTeam, "STGame.Team.Enemy", "Enemy Team Root");
	
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Team_Player_Team1, "STGame.Team.Player.Team1", "PlayerTeam1");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Team_Enemy_Team1, "STGame.Team.Enemy.Team1", "EnemyTeam1");

	// 以下是属性相关的游戏玩法标签定义
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Attributes_Health, "STGame.Attributes.Health", "血量");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Attributes_MaxHealth, "STGame.Attributes.MaxHealth", "最大血量");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Attributes_Stamina, "STGame.Attributes.Stamina", "气力值");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Attributes_MaxStamina, "STGame.Attributes.MaxStamina", "最大气力值");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Attributes_StaminaRegeneration, "STGame.Attributes.StaminaRegeneration", "气力值自动恢复");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Attributes_Energy, "STGame.Attributes.Energy", "能量值");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Attributes_MaxEnergy, "STGame.Attributes.MaxEnergy", "最大能量值");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Attributes_EnergyBean, "STGame.Attributes.EnergyBean", "能量豆");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Attributes_MaxEnergyBean, "STGame.Attributes.MaxEnergyBean", "能量豆上限");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Attributes_Attack, "STGame.Attributes.Attack", "攻击力");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Attributes_Tenacity, "STGame.Attributes.Tenacity", "抗硬直");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Attributes_Stun, "STGame.Attributes.Stun", "当前眩晕值");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Attributes_MaxStun, "STGame.Attributes.MaxStun", "最大眩晕值");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Attributes_Shield, "STGame.Attributes.Shield", "护盾");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Attributes_MaxShield, "STGame.Attributes.MaxShield", "最大护盾");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Attributes_DamageReduction, "STGame.Attributes.DamageReduction", "减伤");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Attributes_LifeFlask, "STGame.Attributes.LifeFlask", "血瓶计数");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Attributes_MaxLifeFlask, "STGame.Attributes.MaxLifeFlask", "最大血瓶计数");

	// 以下是状态相关的游戏玩法标签定义
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(State_InAttackWarning, "STGame.State.InAttackWarning", "处于AttackWarning中");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(State_AttackState, "STGame.State.AttackState", "处于AttackState中");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(State_Invincible, "STGame.State.Invincible", "无敌");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(State_ImmueStiff, "STGame.State.ImmueStiff", "免疫硬直");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(State_Stiff, "STGame.State.Stiff", "硬直");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(State_InTTTB, "STGame.State.InTTTB", "铜头铁臂");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(State_BounceAttack, "STGame.State.BounceAttack", "铜头铁臂弹刀成功");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(State_ChargeLoopEnd, "STGame.State.ChargeLoopEnd", "蓄力技能提前跳入LoopEnd");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(State_Stunning, "STGame.State.Stunning", "眩晕状态中");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(State_DisableShield, "STGame.State.DisableShield", "禁用护盾");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(State_InFormation, "STGame.State.Battle.InFormation", "阵形状态");
	
	// 以下是输入相关的游戏玩法标签定义
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Input_DodgeInputCache, "STGame.Input.Cache.DodgeInput", "允许缓存翻滚指令");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Input_ComboInputCache, "STGame.Input.Cache.ComboInput", "允许缓存攻击指令"); 

	// 以下是行为相关的游戏玩法标签定义
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Actions_Attack, "STGame.Action.Attack", "轻攻击");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Actions_HeavyAttack, "STGame.Action.HeavyAttack", "重攻击");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Actions_Dodge, "STGame.Action.Dodge", "翻滚");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Actions_PreciseDodge, "STGame.Action.PreciseDodge", "精准闪避");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Actions_BeHit, "STGame.Action.BeHit", "受击");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Actions_Dead, "STGame.Action.Dead", "死亡");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Actions_MakeDamage, "STGame.Action.MakeDamage", "造成伤害");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Actions_Tandao, "STGame.Action.Tandao", "弹刀");

	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Message_Damage, "STGame.Message.Damage", "伤害事件");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Message_GroupAICharacterSpawn, "STGame.Message.GroupAICharacterSpawn", "GroupAI角色创建");
}
