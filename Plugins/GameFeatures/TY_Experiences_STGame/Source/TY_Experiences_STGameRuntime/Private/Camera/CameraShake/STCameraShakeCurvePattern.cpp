// Copyright (C) Developed by Pask, Published by Dark Tower Interactive SRL 2023. All Rights Reserved. 


#include "Camera/CameraShake/STCameraShakeCurvePattern.h"

void USTCameraShakeCurvePattern::StartShakePatternImpl(const FCameraShakePatternStartParams& Params)
{
	Super::StartShakePatternImpl(Params);

	Timer = 0.0f;
}

void USTCameraShakeCurvePattern::UpdateShakePatternImpl(const FCameraShakePatternUpdateParams& Params,
	FCameraShakePatternUpdateResult& OutResult)
{
	Timer += Params.DeltaTime;
	OutResult.Location = this->LocationCurve.GetValue(Timer);
	
	const float BlendWeight = State.Update(Params.DeltaTime);
	OutResult.ApplyScale(BlendWeight);
}

void USTCameraShakeCurvePattern::ScrubShakePatternImpl(const FCameraShakePatternScrubParams& Params,
	FCameraShakePatternUpdateResult& OutResult)
{
	Timer = Params.AbsoluteTime;
	OutResult.Location = this->LocationCurve.GetValue(Timer);

	const float BlendWeight = State.Scrub(Params.AbsoluteTime);
	OutResult.ApplyScale(BlendWeight);
}
