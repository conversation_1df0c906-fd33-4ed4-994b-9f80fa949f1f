
[/Script/Crossfader.CrossfaderSettings]
+BaseProjectMixes=/MatrixSample/Audio/Modulation/ControlBusMix/CBM_BaseMix.CBM_BaseMix
UserMix=None
+MixStateBanks=/MatrixSample/Audio/Crossfader/CitySampleMixStateBank.CitySampleMixStateBank

[Internationalization]
+LocalizationPaths=%GAMEDIR%Content/Localization/CitySample
+CultureMappings=es-AR;es-419
+CultureMappings=es-BO;es-419
+CultureMappings=es-CL;es-419
+CultureMappings=es-CO;es-419
+CultureMappings=es-CR;es-419
+CultureMappings=es-CU;es-419
+CultureMappings=es-DO;es-419
+CultureMappings=es-EC;es-419
+CultureMappings=es-GT;es-419
+CultureMappings=es-HN;es-419
+CultureMappings=es-MX;es-419
+CultureMappings=es-NI;es-419
+CultureMappings=es-PA;es-419
+CultureMappings=es-PE;es-419
+CultureMappings=es-PR;es-419
+CultureMappings=es-PY;es-419
+CultureMappings=es-SV;es-419
+CultureMappings=es-US;es-419
+CultureMappings=es-UY;es-419
+CultureMappings=es-VE;es-419


[/Script/WorldAudioDataSystem.WorldAudioDataSettings]
MantleTypeToSoundscapeColorPointMap=(("building", (TagName="Soundscape.WorldAudioData.Building")),("sidewalk", (TagName="Soundscape.WorldAudioData.Sidewalk")),("2-lane-Roads", (TagName="Soundscape.WorldAudioData.Road")),("4-lane-Roads", (TagName="Soundscape.WorldAudioData.Road")),("6-lane-Roads", (TagName="Soundscape.WorldAudioData.Road")),("concrete", (TagName="Soundscape.WorldAudioData.Concrete")),("freeway", (TagName="Soundscape.WorldAudioData.Freeway")),("interchange", (TagName="Soundscape.WorldAudioData.Interchange")),("shoreline", (TagName="Soundscape.WorldAudioData.Shoreline")),("zone_bus", (TagName="Soundscape.WorldAudioData.ZoneBus")),("zone_exempt", (TagName="Soundscape.WorldAudioData.ZoneExempt")),("zone_parking", (TagName="Soundscape.WorldAudioData.ZoneParking")),("zone_plaza", (TagName="Soundscape.WorldAudioData.ZonePlaza")),("zone_truck", (TagName="Soundscape.WorldAudioData.ZoneTruck")),("piers", (TagName="Soundscape.WorldAudioData.Pier")))
ContinuousPawnTags=("Pawn_City","Pawn_HiAlt","Pawn_Wind")
ContinuousSoundMap=(("freeway", "/MatrixSample/Audio/MetaSounds/Ambient/sfx_amb_Continuous_Highway_lp_meta.sfx_amb_Continuous_Highway_lp_meta"),("interchange", "/MatrixSample/Audio/MetaSounds/Ambient/sfx_amb_Continuous_Highway_lp_meta.sfx_amb_Continuous_Highway_lp_meta"),("shoreline", "/MatrixSample/Audio/SoundWaves/Ambient/Air_Traffic/sfx_amb_env_MovingAir_lp.sfx_amb_env_MovingAir_lp"),("Pawn_City", "/MatrixSample/Audio/MetaSounds/Ambient/sfx_amb_Urban_Air_lp_meta.sfx_amb_Urban_Air_lp_meta"),("Pawn_HiAlt", "/MatrixSample/Audio/MetaSounds/Ambient/sfx_amb_Pawn_HiAltAir_lp_meta.sfx_amb_Pawn_HiAltAir_lp_meta"),("Pawn_Wind", "/MatrixSample/Audio/MetaSounds/Ambient/sfx_amb_Pawn_Wind_lp_meta.sfx_amb_Pawn_Wind_lp_meta"))
MantleEffectsMetaDataKey=reverb
MaxEffectAltitude=1000.000000
+EffectMap=(SoundSubmix="/MatrixSample/Audio/Submixes/MainReverbSubmix.MainReverbSubmix",IntensityToEffectMap=((MinimumEffectRange=11,SubmixEffectPresetChain=("/MatrixSample/Audio/Effects/HeavyReverbPreset.HeavyReverbPreset"))))
WorldAudioDataGameplayScript=BlueprintGeneratedClass'/MatrixSample/Audio/Scripts/CitySampleWorldAudioDataScript.CitySampleWorldAudioDataScript_C'
MASSTrafficCarConfigurationPresetMap=(("Default", "/MatrixSample/Audio/Vehicles/NPC_Presets/WAD_NPC_Hatchback_Preset.WAD_NPC_Hatchback_Preset"),("Bus", "/MatrixSample/Audio/Vehicles/NPC_Presets/WAD_NPC_Bus_Preset.WAD_NPC_Bus_Preset"),("GarbageTruck", "/MatrixSample/Audio/Vehicles/NPC_Presets/WAD_NPC_GarbageTruck_Preset.WAD_NPC_GarbageTruck_Preset"),("Hatchback", "/MatrixSample/Audio/Vehicles/NPC_Presets/WAD_NPC_Hatchback_Preset.WAD_NPC_Hatchback_Preset"),("Sedan", "/MatrixSample/Audio/Vehicles/NPC_Presets/WAD_NPC_Sedan_Preset.WAD_NPC_Sedan_Preset"),("SUV", "/MatrixSample/Audio/Vehicles/NPC_Presets/WAD_NPC_SUV_Preset.WAD_NPC_SUV_Preset"),("SportsCar", "/MatrixSample/Audio/Vehicles/NPC_Presets/WAD_NPC_SportsCar_Preset.WAD_NPC_SportsCar_Preset"),("PickupTruck", "/MatrixSample/Audio/Vehicles/NPC_Presets/WAD_NPC_SUV_Preset.WAD_NPC_SUV_Preset"),("Minivan", "/MatrixSample/Audio/Vehicles/NPC_Presets/WAD_NPC_Minivan_Preset.WAD_NPC_Minivan_Preset"),("Semi", "/MatrixSample/Audio/Vehicles/NPC_Presets/WAD_NPC_Semi_Preset.WAD_NPC_Semi_Preset"),("ParcelTruck", "/MatrixSample/Audio/Vehicles/NPC_Presets/WAD_NPC_ParcelTruck_Preset.WAD_NPC_ParcelTruck_Preset"))

[/Script/Soundscape.SoundscapeSettings]
SoundscapePaletteCollection=()
bDebugDraw=False
LOD1ColorPointHashWidth=500.000000
LOD1ColorPointHashDistance=4600.000000
LOD2ColorPointHashWidth=5500.000000
LOD2ColorPointHashDistance=10000.000000
LOD3ColorPointHashWidth=9500.000000
ActiveColorPointHashWidth=1000.000000
ActiveColorPointHashUpdateTimeSeconds=0.500000

[CookedEditorSettings]
bSupportCookedEditor=true
bStageRestrictedDirs=true
+DisabledPlugins=NeuralNetworkInference
+DisabledPlugins=RemoteControl
+DisabledPlugins=Text3D
CookedEditorTargetName=CitySampleEditorCooked