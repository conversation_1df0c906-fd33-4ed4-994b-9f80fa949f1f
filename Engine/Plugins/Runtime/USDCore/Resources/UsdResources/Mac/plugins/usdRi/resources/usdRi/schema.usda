#usda 1.0
(
    "This file describes the USD RenderMan Interface (UsdRi) schemata for code generation."
    subLayers = [
        @usd/schema.usda@,
        @usdShade/schema.usda@,
    ]
)

def "GLOBAL" (
    customData = {
        string libraryName      = "usdRi"
        string libraryPath      = "pxr/usd/usdRi"
        # string libraryPrefix  = "UsdRi"
        # string tokensPrefix   = "UsdRi"
        # dictionary libraryTokens = {}
        dictionary libraryTokens = {
            dictionary "renderContext" = {
                string value = "ri"
                string doc = "UsdShadeMaterial / Hydra render context token for UsdRi"
            }
            dictionary spline = {
                string doc = "UsdSplineAPI - Namespace for spline attributes"
            }
            dictionary interpolation = {
                string doc = "UsdSplineAPI - Interpolation attribute name"
            }
            dictionary positions = {
                string doc = "UsdSplineAPI - Positions attribute name"
            }
            dictionary values = {
                string doc = "UsdSplineAPI - values attribute name"
            }
            dictionary linear = {
                string doc = "UsdSplineAPI - Linear spline interpolation"
            }
            dictionary bspline = {
                string doc = "UsdSplineAPI - BSpline spline interpolation"
            }
            dictionary "catmullRom" = {
                string value = "catmull-rom"
                string doc = "UsdSplineAPI - Catmull-Rom spline interpolation"
            }
            dictionary constant = {
                string doc = "UsdSplineAPI - Constant-value spline interpolation"
            }
            dictionary cameraVisibility = {
                string doc = """
                UsdRenderPassAPI - This token represents the collection 
                name to use with UsdCollectionAPI to set the camera visibility
                attribute on the prims in the collection for the RenderPass.
                """
            }
            dictionary matte = {
                string doc = """
                UsdRenderPassAPI - This token represents the collection 
                name to use with UsdCollectionAPI to set the matte
                attribute on the prims in the collection for the RenderPass.
                """
            }
        }
    }
)
{
}

class "StatementsAPI" (
    inherits = </APISchemaBase>
    doc = """Container namespace schema for all renderman statements.

    \\note The longer term goal is for clients to go directly to primvar
    or render-attribute API's, instead of using UsdRi StatementsAPI
    for inherited attributes.  Anticpating this, StatementsAPI
    can smooth the way via a few environment variables:

    * USDRI_STATEMENTS_READ_OLD_ENCODING: Causes StatementsAPI to read
      old-style attributes instead of primvars in the "ri:"
      namespace.
    """ 
    customData = {
        string extraIncludes = """
#include "pxr/usd/usdGeom/primvarsAPI.h"
"""
    }
) {
}

### Material API common to both RSL and RIS ###

class "RiMaterialAPI" (
    inherits = </APISchemaBase>
    doc = """
    \\deprecated Materials should use UsdShadeMaterial instead.
    This schema will be removed in a future release.

    This API provides outputs that connect a material prim to prman 
    shaders and RIS objects."""
    customData = {
        # We want the class name to be UsdRiMaterialAPI
        string className = "MaterialAPI"    
        string extraIncludes = """
#include "pxr/usd/usdShade/input.h"
#include "pxr/usd/usdShade/output.h"
#include "pxr/usd/usdShade/material.h"
"""
    }
) {
    token outputs:ri:surface (
        displayGroup = "Outputs"
        customData = {
            string apiName = "surface"
        }
    )
    token outputs:ri:displacement (
        displayGroup = "Outputs"
        customData = {
            string apiName = "displacement"
        }
    )
    token outputs:ri:volume (
        displayGroup = "Outputs"
        customData = {
            string apiName = "volume"
        }
    )
}


########################################################################
# Lighting API

class "RiSplineAPI" (
    inherits = </APISchemaBase>
    doc = """
    \\deprecated This API schema will be removed in a future release.

    RiSplineAPI is a general purpose API schema used to describe
    a named spline stored as a set of attributes on a prim.
    
    It is an add-on schema that can be applied many times to a prim with
    different spline names. All the attributes authored by the schema
    are namespaced under "$NAME:spline:", with the name of the
    spline providing a namespace for the attributes.

    The spline describes a 2D piecewise cubic curve with a position and
    value for each knot. This is chosen to give straightforward artistic
    control over the shape. The supported basis types are:

    - linear (UsdRiTokens->linear)
    - bspline (UsdRiTokens->bspline)
    - Catmull-Rom (UsdRiTokens->catmullRom)
    """
    customData = {
        string className = "SplineAPI"
    }
) {
}


########################################################################
# Render API

class "RiRenderPassAPI" (
    inherits = </APISchemaBase>
    doc = """
    RiRenderPassAPI is an API schema that provides a mechanism
    to set certain Ri statements on each prim in a collection,
    for a given RenderPass prim.

    \\anchor usdRi_cameraVisibility
    The objects that are relevant to the render is specified via the 
    cameraVisibility collection (UsdCollectionAPI) and can be accessed via 
    GetCameraVisibilityCollectionAPI(). Each prim in the collection will have
    ri:visible:camera set to 1.  By default everything in the scene should be 
    visible to camera, so this collection sets includeRoot to 1.

    \\anchor usdRi_matte
    The objects that are relevant to the render is specified via the 
    matte collection (UsdCollectionAPI) and can be accessed via 
    GetMatteCollectionAPI().  Each prim in the collection will have
    ri:matte set to 1.  By default everything in the scene should render
    normally, so this collection sets includeRoot to 0.
    """
    customData = {
        string className = "RenderPassAPI"
        token[] apiSchemaAutoApplyTo = ["RenderPass"]
        token[] apiSchemaCanOnlyApplyTo = ["RenderPass"]
        string extraIncludes = """
#include "pxr/usd/usd/collectionAPI.h" """
    }
    prepend apiSchemas = [
    "CollectionAPI:matte", "CollectionAPI:cameraVisibility"]
) {

    uniform bool collection:cameraVisibility:includeRoot = 1 (
        customData = {
            bool apiSchemaOverride = true
        }
    )
}