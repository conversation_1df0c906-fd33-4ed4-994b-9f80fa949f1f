// Copyright Epic Games, Inc. All Rights Reserved.

#include "/Engine/Private/Common.ush"

uint3 GridSize;
RWBuffer<int>	GridDestinationBuffer;

[numthreads(THREAD_COUNT, THREAD_COUNT, THREAD_COUNT)]
void MainCS(uint3 DispatchThreadId : SV_DispatchThreadID)
{
	if (all(DispatchThreadId < GridSize))
	{
		const int GridIndex = DispatchThreadId.x * GridSize.y * GridSize.z + DispatchThreadId.y * GridSize.z + DispatchThreadId.z;
		GridDestinationBuffer[GridIndex] = 0;
	}
}