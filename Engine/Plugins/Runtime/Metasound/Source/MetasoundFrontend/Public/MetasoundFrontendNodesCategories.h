// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"

namespace Metasound
{
	namespace NodeCategories
	{
		extern const FText METASOUNDFRONTEND_API Conversions;
		extern const FText METASOUNDFRONTEND_API EnumConversions;
		extern const FText METASOUNDFRONTEND_API Functions;
		extern const FText METASOUNDFRONTEND_API Graphs;
		extern const FText METASOUNDFRONTEND_API Inputs;
		extern const FText METASOUNDFRONTEND_API Outputs;
		extern const FText METASOUNDFRONTEND_API Variables;
	} // namespace NodeCategories
} // namespace Metasound
