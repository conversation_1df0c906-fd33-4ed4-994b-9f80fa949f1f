// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "Kismet/BlueprintFunctionLibrary.h"
#include "AndroidPermissionFunctionLibrary.generated.h"

class UAndroidPermissionCallbackProxy;

UCLASS()
class ANDROIDPERMISSION_API UAndroidPermissionFunctionLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()
public:
	/** check if the permission is already granted */
	UFUNCTION(BlueprintCallable, meta = (DisplayName = "Check Android Permission"), Category="AndroidPermission")
	static bool CheckPermission(const FString& permission);

	/** try to acquire permissions and return a singleton callback proxy object containing OnPermissionsGranted delegate */
	UFUNCTION(BlueprintCallable, meta = (DisplayName = "Request Android Permissions"), Category="AndroidPermission")
	static UAndroidPermissionCallbackProxy* AcquirePermissions(const TArray<FString>& permissions);

public:
	/** initialize java objects and cache them for further usage. called when the module is loaded */
	static void Initialize();
};

#if UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_2
#include "CoreMinimal.h"
#endif
