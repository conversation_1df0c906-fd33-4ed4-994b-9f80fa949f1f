// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "BaseCharacterFXEditorMode.h"
#include "Dataflow/DataflowContent.h"
#include "DataflowEditorMode.generated.h"

class UDataflowComponent;
class FDataflowConstructionScene;
class SDataflowGraphEditor;
class FDataflowConstructionViewportClient;
class FDataflowSimulationScene;
class FDataflowSimulationViewportClient;
class IDataflowEditorToolBuilder;
struct FManagedArrayCollection;
class UEdGraphNode;
class FViewport;
class UDataflowEditor;
namespace UE::Dataflow
{
	class IDataflowConstructionViewMode;
}

/**
 * The dataflow editor mode is the mode used in the cloth asset editor. It holds most of the inter-tool state.
 * We put things in a mode instead of directly into the asset editor in case we want to someday use the mode
 * in multiple asset editors.
 */
UCLASS(Transient)
class DATAFLOWEDITOR_API UDataflowEditorMode final : public UBaseCharacterFXEditorMode
{
	GENERATED_BODY()

public:

	const static FEditorModeID EM_DataflowEditorModeId;

	UDataflowEditorMode();

	void SetDataflowEditor(UDataflowEditor* InDataflowEditor);
	TWeakPtr<FDataflowConstructionViewportClient, ESPMode::ThreadSafe> GetConstructionViewportClient() { return ConstructionViewportClient; }

	/**
	* Gets the scene bounding box
	*/
	virtual FBox SceneBoundingBox() const override;

	// Bounding box for selected rest space mesh components
	FBox SelectionBoundingBox() const;

	/**
	* Gets the tool target requirements for the mode.
	*/
	static const FToolTargetTypeRequirements& GetToolTargetRequirements();

	/**
	* Construction View Mode
	*/
	void SetConstructionViewMode(const FName& NewViewModeName);
	const UE::Dataflow::IDataflowConstructionViewMode* GetConstructionViewMode() const;
	bool CanChangeConstructionViewModeTo(const FName& NewViewModeName) const;

	void ToggleConstructionViewWireframe();
	bool CanSetConstructionViewWireframeActive() const;
	bool IsConstructionViewWireframeActive() const
	{
		return bConstructionViewWireframe;
	}

	/** Set the construction scene to store the dynamic meshes generated by the tools */
	void SetDataflowConstructionScene(FDataflowConstructionScene* DataflowPreviewScene);
	FDataflowConstructionScene* GetDataflowConstructionScene() { return ConstructionScene; }
	const FDataflowConstructionScene* GetDataflowConstructionScene() const { return ConstructionScene; }

	/** Set the simulation scene to store the simulation components */
	void SetDataflowSimlationScene(FDataflowSimulationScene* InSimulationScene);
	FDataflowSimulationScene* GetDataflowSimulationScene() { return SimulationScene; }
	const FDataflowSimulationScene* GetDataflowSimulationScene() const { return SimulationScene; }

	/** Set the data flow graph editor to create nodes once the tools have ended*/
	void SetDataflowGraphEditor(TSharedPtr<SDataflowGraphEditor> DataflowGraphEditor);

	TObjectPtr<UEditorInteractiveToolsContext> GetActiveToolsContext()
	{
		return ActiveToolsContext;
	}


	void StartToolForSelectedNode(const UObject* SelectedNode);

private:
	friend class FDataflowEditorToolkit;
	friend class FDataflowEditorModeToolkit;

	// UEdMode
	virtual void Enter() final;
	virtual void Exit() override;
	virtual void ModeTick(float DeltaTime) override;
	virtual bool ShouldToolStartBeAllowed(const FString& ToolIdentifier) const override;
	virtual void OnToolStarted(UInteractiveToolManager* Manager, UInteractiveTool* Tool) override;
	virtual void OnToolEnded(UInteractiveToolManager* Manager, UInteractiveTool* Tool) override;
	virtual void CreateToolkit() override;
	virtual void BindCommands() override;


	// UBaseCharacterFXEditorMode
	virtual void AddToolTargetFactories() override;
	virtual void RegisterTools() override;
	virtual void CreateToolTargets(const TArray<TObjectPtr<UObject>>& AssetsIn) override;
	virtual void InitializeTargets(const TArray<TObjectPtr<UObject>>& AssetsIn) override;

	// Use this function to register tools rather than UEdMode::RegisterTool() because we need to specify the ToolsContext
	void RegisterDataflowTool(TSharedPtr<FUICommandInfo> UICommand,
							  FString ToolIdentifier,
							  UInteractiveToolBuilder* Builder,
							  UEditorInteractiveToolsContext* const ToolsContext,
							  EToolsContextScope ToolScope = EToolsContextScope::Default);

	// Functions for quickly adding a node to the graph, e.g. when triggered by a toolbar button
	void AddNode(FName NewNodeType);
	bool CanAddNode(FName NewNodeType) const;

	void SetSimulationViewportClient(TWeakPtr<FDataflowSimulationViewportClient, ESPMode::ThreadSafe>);
	void RefocusSimulationViewportClient();
	void FirstTimeFocusSimulationViewport();

	void SetConstructionViewportClient(TWeakPtr<FDataflowConstructionViewportClient, ESPMode::ThreadSafe>);
	void RefocusConstructionViewportClient();
	void FirstTimeFocusConstructionViewport();
	bool IsComponentSelected(const UPrimitiveComponent* InComponent);
	void OnDataflowNodeDeleted(const TSet<UObject*>& DeletedNodes);

	/**
	* Return the single selected node in the Dataflow Graph Editor only if it has an output of the specified type
	* If there is not a single node selected, or if it does not have the specified output, return null
	*/
	UEdGraphNode* GetSingleSelectedNodeWithOutputType(const FName& SelectedNodeOutputTypeName) const;

	/**
	 * Create a node with the specified type in the graph
	*/
	UEdGraphNode* CreateNewNode(const FName& NewNodeTypeName);

	/** Create a node with the specified type, then connect it to the output of the specified UpstreamNode.
	* If the specified output of the upstream node is already connected to another node downstream, we first break
	* that connecttion, then insert the new node along the previous connection.
	* We want to turn this:
	*
	* [UpstreamNode] ----> [DownstreamNode(s)]
	*
	* to this:
	*
	* [UpstreamNode] ----> [NewNode] ----> [DownstreamNode(s)]
	*
	*
	* @param NewNodeTypeName The type of node to create, by name
	* @param UpstreamNode Node to connect the new node to
	* @param ConnectionTypeName The type of output of the upstream node to connect our new node to
	* @param NewNodeConnectionName The name of the input/output connection on our new node that will be connected
	* @return The newly-created node
	*/
	UEdGraphNode* CreateAndConnectNewNode(const FName& NewNodeTypeName, UEdGraphNode& UpstreamNode, const FName& ConnectionTypeName, const FName& NewNodeConnectionName);

	/** On all nodes in the graph, set whether the wireframe rendering toggle button is enabled */
	void SetWireframeRenderToggleEnabled(bool bEnable);

	void InitializeContextObject();
	void DeleteContextObject();

	/** Dataflow construction scene from the toolkit */
	FDataflowConstructionScene* ConstructionScene = nullptr;

	/** Dataflow simulation scene from the toolkit */
	FDataflowSimulationScene* SimulationScene = nullptr;

	/** Correspondence between node types and commands to add the node to the graph */
	TMap<FName, TSharedPtr<const FUICommandInfo>> NodeTypeToAddNodeCommandMap;

	/** Correspondence between node types and commands to launch tools */
	TMap<FName, TSharedPtr<const FUICommandInfo>> NodeTypeToToolCommandMap;

	/** Graph editor is required to add nodes once some tools are complete */
	TWeakPtr<SDataflowGraphEditor> DataflowGraphEditor;

	// Points to a view mode owned by FRenderingViewModeFactory
	const UE::Dataflow::IDataflowConstructionViewMode* ConstructionViewMode = nullptr;

	// Whether we should restore the previous view mode when a tool ends
	bool bShouldRestoreSavedConstructionViewMode = false;
	
	// The Construction view mode that was active before starting the current tool. When the tool ends, restore this view mode if bShouldRestoreSavedConstructionViewMode is true.
	FName SavedConstructionViewMode;

	bool bConstructionViewWireframe = false;
	bool bShouldRestoreConstructionViewWireframe = false;

	UPROPERTY()
	TObjectPtr<UEditorInteractiveToolsContext> ActiveToolsContext = nullptr;

	TWeakPtr<FDataflowConstructionViewportClient, ESPMode::ThreadSafe> ConstructionViewportClient;
	TWeakPtr<FDataflowSimulationViewportClient, ESPMode::ThreadSafe> SimulationViewportClient;

	// The first time we get a valid mesh, refocus the camera on it
	bool bFirstValid2DMesh = true;
	bool bFirstValid3DMesh = true;

	// Whether the rest space viewport should focus on the rest space mesh on the next tick
	bool bShouldFocusConstructionView = true;
	bool bShouldFocusSimulationView = true;

	//@todo(Dataflow) : Move to Construction and Simulaiton
	void ConstructionViewportResized(FViewport* ConstructionViewport, uint32 Unused);
	void SimulationViewportResized(FViewport* ConstructionViewport, uint32 Unused);

	// Dataflow node type whose corresponding tool should be started on the next Tick
	FName NodeTypeForPendingToolStart;

	// If we should start the tool for the selected node on the next ModeTick call. Used when switching ViewModes to allow the scene to be rebuilt before restarting the tool.
	bool bShouldRestartToolNextTick = false;

	// Whether we have a single mesh selected in the Construction Scene prior to shutting down a running tool. The Scene will likely be rebuilt when the tool ends, resulting in
	// losing selection information, so we save this before shutting the tool down.
	bool bHadSingleSelectionBeforeToolShutdown = false;

	UDataflowEditor* DataflowEditor = nullptr;

	// Timestamp for telemetry
	FDateTime LastModeStartTimestamp;

};

