<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>Real-Time Geometric Glint</Name>
<!-- Software Name and Version  -->
<!-- Software Name: Real-Time Geometric Glint
    Version:-->
    <EndUserGroup>Git</EndUserGroup>
  <Location>UE5/Main/Engine/Source/Runtime/Renderer/ThirdParty/Glint</Location>
  <Function>I have converted their LUT that need to be sampled at runtime to a UE5 texture loaded on demand when needed.The glint technique is used in shaders by our material Slab node to simulate glints. The researched shader code has been adapted to compile in unreal together with some optimizations and other improvements ideas we have in progress.
</Function>
  <Eula>https://github.com/ASTex-ICube/aa_real_time_glint/blob/main/LICENSE</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder></LicenseFolder>
</TpsData>



