;
; Base Driver Deny List
; =====================
;
; Hardware checks are controlled by the console variable "r.WarnOfBadDrivers". The the console variable options for more information.
;
; What to do when a certain driver or hardware shows problems:
;  - If the driver exhibiting the problem is old, add it to the DriverDenyList with "<=". In general it's better to ask users to upgrade.
;  - If the driver exhibiting the problem is the latest, add it to the DriverDenyList with ">=". We should not assume the next driver version fixes the problem.
;
; Expectations on QA:
;  - QA always tests the latest driver version.
;  - When a new driver is released, QA should test it and if it does not introduce bugs the SuggestedDriverVersion should be updated to the latest driver.
;  - If the latest driver is denied, it is not removed until QA verifies it is fixed.
;  - If driver specific bugs are found, enter them in JIRA so that they can be tracked.
;
; Expectations on Engineering:
;  - Bugs on older drivers are added to the DriverDenyList and otherwise ignored.
;  - Bugs on the latest driver are added to the DriverDenyList but treated as critical bugs for which we must try to find a workaround. We must also notify the IHV to help if we cannot find the problem.
;
; INI sections:
;    [Vendor OS] where
;    Vendor: GPU_NVIDIA, GPU_AMD, GPU_Intel
;    OS: Windows, Linux
;
; Explanation of the INI entries:
;  - "SuggestedDriverVersion": string used to communicate to the user which driver version is appropriate (not used by any code), empty if unknown.
;		Can use multiple so we can add per RHI appending ";D3D12" for instance for a specific RHI; having no ';RHI' will return that one if the RHI is not found
;  - "DriverDenyList": array of tests that can be made to mark a driver or a range of them as problematic
;    "DriverVersion": to compare against the "unified driver" (see log) version e.g. "123.45.2" "==123.45" "<123.45", comparison operators: == != > < >= <=
;    "Reason": string used to document the reason for denying the driver, could be shown to the user
;    comment above each deny list entry: more details about the problem, potential workarounds
;
;-----------------------------------------------------------------------------------------------------------------

[GPU_NVIDIA Windows]
SuggestedDriverVersion="536.40"

; The newest driver which supports legacy Nvidia GPUs (700 series and older) is 474.64. We allow this driver for DX11.
+DriverDenyList=(DriverVersion="<474.64", Reason="These driver versions have known stability issues and missing features", RHI="D3D11")

; DX12 and Vulkan require recent drivers. The 536 series was released in June 2023.
+DriverDenyList=(DriverVersion="<536.00", Reason="These driver versions have known stability issues and missing features", RHI="D3D12")
+DriverDenyList=(DriverVersion="<536.00", Reason="These driver versions have known stability issues and missing features", RHI="Vulkan")

;-----------------------------------------------------------------------------------------------------------------

[GPU_NVIDIA Linux]
SuggestedDriverVersion="535.86"

; The 535 series of the Linux driver was released in June 2023.
+DriverDenyList=(DriverVersion="<535.00", Reason="These driver versions have known stability issues and missing features")

;-----------------------------------------------------------------------------------------------------------------

[GPU_AMD Windows]
SuggestedDriverVersion="24.3.1"

; The newest driver which supports legacy AMD GPUs (GCN1-GCN3 chips like the Radeon R9 or older) is 22.6.1. We allow this driver for DX11.
+DriverDenyList=(DriverVersion="<27.20.20913.2000", Reason="These driver versions have known stability issues and missing features", RHI="D3D11")

; The minimum version for DX12 is Vulkan is 24.3.1, published in March 2024. There are separate packages for different GPU generations and they
; have different internal versions, so we can't check for a version number explicitly (the RDNA driver is 31.0.24027.1012, older GPUs have
; 31.0.21912.14). We'll use a driver date check instead, as both packages have internal date stamps newer than Feb 19th.
+DriverDenyList=(DriverDate="<2-19-2024", Reason="These driver versions have known stability issues and missing features", RHI="D3D12")
+DriverDenyList=(DriverDate="<2-19-2024", Reason="These driver versions have known stability issues and missing features", RHI="Vulkan")

;-----------------------------------------------------------------------------------------------------------------

[GPU_AMD Linux]
SuggestedDriverVersion="23.3.1"

; RADV builds older than 23.3.1 have issues running UE5, so we'll use this as the minimum version (released December 2023). Note that it's also
; possible to use the AMD proprietary driver on Linux, and we don't distinguish between driver vendors. The current version for the AMD driver as
; of writing this is 23.40.2, so 23.3.1 is a valid version for that package as well (although a fairly old one).
+DriverDenyList=(DriverVersion="<23.3.1", Reason="These driver versions have known stability issues and missing features")

;-----------------------------------------------------------------------------------------------------------------

[GPU_Intel Windows]
SuggestedDriverVersion="101.5333"

; Legacy integrated GPUs are supported by several driver packages (depending on CPU generation) and they all have different version numbers, so
; we will check the date instead. The latest driver for Ivy Bridge chips is 105.161, dated Aug 6th 2020, so we will allow that for D3D11.
+DriverDenyList=(DriverDate="<8-6-2020", Reason="These driver versions have known stability issues and missing features", RHI="D3D11")

; DX12 and Vulkan should not be used on integrated GPUs. ARC and newer chips require recent drivers. 101.5333 was released in February 2024.
+DriverDenyList=(DriverVersion="<101.5333", Reason="These driver versions have known stability issues and missing features", RHI="D3D12")
+DriverDenyList=(DriverVersion="<101.5333", Reason="These driver versions have known stability issues and missing features", RHI="Vulkan")

;-----------------------------------------------------------------------------------------------------------------

[GPU_Intel Linux]
SuggestedDriverVersion="30.0.101.1340"
