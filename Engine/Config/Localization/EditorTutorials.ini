[CommonSettings]
ManifestDependencies=Content/Localization/Engine/Engine.manifest
ManifestDependencies=Content/Localization/Editor/Editor.manifest
ManifestDependencies=Content/Localization/Category/Category.manifest
ManifestDependencies=Content/Localization/Keywords/Keywords.manifest
ManifestDependencies=Content/Localization/PropertyNames/PropertyNames.manifest
ManifestDependencies=Content/Localization/ToolTips/ToolTips.manifest
SourcePath=Content/Localization/EditorTutorials
DestinationPath=Content/Localization/EditorTutorials
ManifestName=EditorTutorials.manifest
ArchiveName=EditorTutorials.archive
PortableObjectName=EditorTutorials.po
NativeCulture=en
CulturesToGenerate=en
CulturesToGenerate=de
CulturesToGenerate=pl
CulturesToGenerate=es
CulturesToGenerate=es-419
CulturesToGenerate=fr
CulturesToGenerate=ja
CulturesToGenerate=ar
CulturesToGenerate=ko
CulturesToGenerate=pt-BR
CulturesToGenerate=it
CulturesToGenerate=ru
CulturesToGenerate=tr
CulturesToGenerate=zh-Hans

;NOTE: IntroTutorials is now moved into the GuidedTutorial plugin.
; That module needs to be loaded first so make sure the plugin is enabled before performing these gathers.

;Gather text from assets
[GatherTextStep0]
CommandletClass=GatherTextFromAssets
IncludePathFilters=Content/Tutorial/*
PackageFileNameFilters=*.umap
PackageFileNameFilters=*.upk
PackageFileNameFilters=*.uasset
bFixBroken=false
ShouldGatherFromEditorOnlyData=true

;Write Manifest
[GatherTextStep1]
CommandletClass=GenerateGatherManifest

;Write Archives
[GatherTextStep2]
CommandletClass=GenerateGatherArchive
bPurgeOldEmptyEntries=true

;Import localized PO files
[GatherTextStep3]
CommandletClass=InternationalizationExport
bImportLoc=true

;Write Localized Text Resource
[GatherTextStep4]
CommandletClass=GenerateTextLocalizationResource
ResourceName=EditorTutorials.locres

;Export to PO files
[GatherTextStep5]
CommandletClass=InternationalizationExport
bExportLoc=true

;Write Text Localization Report
[GatherTextStep6]
CommandletClass=GenerateTextLocalizationReport
DestinationPath=\\epicgames.net\root\UE3\Localization\WordCounts
bWordCountReport=true
WordCountReportName=EditorTutorials.csv
bConflictReport=true
; Different file extensions will output the report in different formats. Currently supported extensions are .txt and .csv
ConflictReportName=EditorTutorialsConflicts.csv